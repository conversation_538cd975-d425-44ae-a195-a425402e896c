@startuml
start
:用户通过谷歌广告进入RedteaGO Web;
:前端保存 来源信息（GOOGLE_ADS）到浏览器本地;
if (注册/登录 携带来源（GOOGLE_ADS）请求接口) then (注册)
  :直接添加 <谷歌广告新用户> 标签;
  stop
else (登录)
  if (查询用户是否有 <谷歌广告新用户> 标签) then (有)
    stop
  else (无)
    if (查询用户是否有 <谷歌广告老用户> 标签) then (有)
        stop
    else (无)
        :"添加 <谷歌广告老用户> 标签";
        stop
    endif
  endif
endif
@enduml

@startuml
start
if (检测用户余额是否大于3刀) then (yes)
  :直接扣除3刀余额;
  stop
else (no)
  :按顺序提取**使用邀请码后**的**余额支付**订单;
  :累计订单总额，直到第一个累计总额 >= 3美元;
  :停止订单;
  switch (订单总额)
    case (等于3刀)
      stop;
    case (大于3刀)
      :返回用户余额 （停掉的订单总额 - 3刀）;
      stop
    case (小于3刀)
      :扣除用户余额 （3刀 - 停掉的订单总额）;
      stop
@enduml


@startuml
start
:查找用户使用邀请码的余额变更记录;
if (用户钱包交易记录存在?) then (否)
  :记录警告日志\n (理论不会到这里);
  stop
else (是)
  :添加处罚记录;
endif

if (扣除邀请奖励成功?) then (是)
  stop
else (否)
  :获取使用余额支付的订单列表\n包含状态（PAID、INREVIEW、REFUNDING、REFUND_FAIL）;
  :汇总订单金额，直到 >= 邀请余额奖励金额;
group 处理逻辑
  if (订单金额总和 = 3美元) then (是)
    :停用相关订单;
    #fdffdb: 如果退款状态\n INREVIEW、REFUNDING、REFUND_FAIL\n则修改为 REFUNDED;
    stop
  else if (订单金额总和 > 3美元) then (是)
    :停用到第一个超过3美元的订单;
    :将差额返回用户余额;
  else (订单金额总和 < 3美元)
    :停用相关订单;
    :从用户余额中扣除差额;
  endif
end group
group 用户余额操作
  if (扣款 or 回款?) then (回款)
    :将差额返回用户余额;
  else (扣款)
    :检查用户余额是否足够;

    if (余额不足?) then (是)
      if (余额为0?) then (是)
        :记录警告日志;
        stop
      else (否)
        :记录警告日志;
        :扣除剩余全部余额;
      endif
    else (否)
      :扣除差额;
    endif
  endif

end group
stop
@enduml
