# 根据客户端和版本匹配 Mammon 配置的支付账号

## 1. 变更概述

本次变更主要实现了基于 clientId 和 appVersion 来匹配 Mammon appPlatform 的功能。通过在支付方式配置中增加客户端平台映射配置，系统可以根据不同客户端和版本返回相应的
Mammon appPlatform 参数，提高了系统的灵活性和适配能力。

## 2. 整体流程

### 2.1 数据流程

1. 客户端发起支付请求，请求中包含 `clientId` 和 `appVersion` 信息
2. 系统根据支付方式 ID 查找对应的支付方式配置
3. 根据 `clientId` 和 `appVersion` 匹配适用的 Mammon appPlatform
    - 首先检查配置是否已过期（deprecated=true），若过期则抛出异常
    - 根据 `clientId` 查找匹配的客户端配置
    - 如果找不到匹配的客户端配置，则使用 payment_method 的 appPlatform 字段
    - 对于 Web 客户端（`clientId` = 0），直接使用匹配到的 appPlatform，不做版本判断
    - 对于其他客户端，检查版本范围：minVersion ≤ 当前版本 < maxVersion
    - 如果版本匹配，则使用配置的 appPlatform，否则使用默认 appPlatform
4. 系统将匹配到的 appPlatform 设置到 Mammon 支付创建信息中
5. 支付系统使用最终的 appPlatform 进行支付创建

### 2.2 版本匹配逻辑

```
if (minVersion不为空 && maxVersion不为空) {
    if (minVersion ≤ 当前版本 < maxVersion) {
        使用配置的appPlatform
    } else {
        使用默认appPlatform
    }
} else if (minVersion不为空 && 当前版本 < minVersion) {
    使用默认appPlatform
} else if (maxVersion不为空 && 当前版本 ≥ maxVersion) {
    使用默认appPlatform
} else {
    使用配置的appPlatform
}
```

## 3. 涉及到的接口

- 创建订单

- 创建余额充值订单

- 创建订单充值（在已购买的订单上充值流量）

## 4. 数据库变动

### 4.1 表结构变更

**payment_methods 表**:

- 新增 `app_platform_mapping` 字段，类型为 `json`
- 字段注释：客户端平台映射配置

### 4.2 变更脚本

```sql
alter table payment_methods
   add column app_platform_mapping json comment '客户端平台映射配置:
1. 根据 client_id 和 appVersion 匹配选择相应的 app_platform
2. 匹配成功则使用配置的 appPlatform，否则使用原 app_platform
3. minVersion 为空不限制最低版本，maxVersion 为空不限制最高版本
4. deprecated 为 true 时，表示该配置已过期，客户端在支付时会抛出异常

示例: [
  {"clientId": 0, "appPlatform": "RedteaGO_20250423"},
  {"clientId": 2001, "appPlatform": "RedteaGO", "maxVersion": "3.7.0", "deprecated": true},
  {"clientId": 2001, "appPlatform": "RedteaGO_20250423", "minVersion": "3.7.0", "maxVersion": "4.7.0"},
  {"clientId": 2001, "appPlatform": "RedteaGO_20250424", "minVersion": "4.7.0"}
]' after app_platform;
```

### 4.3 新增 WEB 客户端配置

```sql
UPDATE Nobel.payment_methods t
SET t.app_platform_mapping = '[
  {
    "clientId": 0,
    "appPlatform": "RedteaGO_20250423"
  }
]'
WHERE t.id = 6;
```

## 5. 配置示例

### 5.1 JSON 格式示例

```json
[
  {
    "clientId": 0,
    "appPlatform": "RedteaGO_20250423",
    "minVersion": "",
    "maxVersion": ""
  },
  {
    "clientId": 2001,
    "appPlatform": "RedteaGO_20250423",
    "minVersion": "3.7.0",
    "maxVersion": ""
  },
  // 客户端 2002 (当前版本 < 3.7.0) 使用 RedteaGO 平台，当 deprecated=true 时，在支付时会抛出异常
  {
    "clientId": 2002,
    "appPlatform": "RedteaGO",
    "minVersion": "",
    "maxVersion": "3.7.0",
    "deprecated": true
  },
  // 客户端 2002 (3.7.0 ≤ 当前版本 < 4.7.0) 使用 RedteaGO_20250423 平台
  {
    "clientId": 2002,
    "appPlatform": "RedteaGO_20250423",
    "minVersion": "3.7.0",
    "maxVersion": "4.7.0"
  },
  // 客户端 2003 (4.7.0 ≤ 当前版本) 使用 RedteaGO_20250424 平台
  {
    "clientId": 2003,
    "appPlatform": "RedteaGO_20250424",
    "minVersion": "4.7.0",
    "maxVersion": ""
  }
]
```

### 5.2 配置说明

- `clientId`: 客户端ID，0 表示 Web 客户端
- `appPlatform`: 指定的 Mammon appPlatform 平台参数
- `minVersion`: 最低支持版本，为空表示不限制，不为空则表示 ≥ minVersion
- `maxVersion`: 最高支持版本，为空表示不限制，不为空则表示 < maxVersion
- `deprecated`: 是否过期，如果设置为 true，则该配置不再可用，客户端在支付时会抛出异常

## 6. 使用场景

- 针对不同客户端使用不同的 Mammon appPlatform 配置
- 针对客户端版本升级，实现平滑过渡支付配置
- 特定客户端版本使用特定的支付平台配置
- 通过设置 deprecated=true 标记配置已过期，用于控制客户端在支付时抛出异常
