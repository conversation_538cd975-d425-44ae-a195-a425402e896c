package com.redteamobile.nobel.bip;

import com.redteamobile.bip.BipPSKManageFactory;
import com.redteamobile.bip.config.ListenerConfig;
import com.redteamobile.bip.handler.TlsHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/02/20 15:02
 * @description
 */
@Configuration
public class CustomBipServerConfiguration {

  @Bean
  public TlsHandler defaultTlsHandler(@Autowired BipPSKManageFactory bipPSKManageFactory) {
    return new TlsHandler(bipPSKManageFactory);
  }

  @Bean
  public BipPSKManageFactory defaultBipPSKManageFactory(ListenerConfig listenerConfig) {
    return new BipPSKManageFactory(listenerConfig);
  }
}