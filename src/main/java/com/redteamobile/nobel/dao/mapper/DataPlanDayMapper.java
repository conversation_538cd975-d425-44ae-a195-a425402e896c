package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.model.entity.mybatis.DataPlanDay;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * DataPlanDay数据库操作接口类
 **/
@Mapper
public interface DataPlanDayMapper extends MybatisBaseMapper<DataPlanDay>{

    /**
     * 插入新纪录
     */
    default int insertRecord(DataPlanDay entity) { return insert(entity); };

    /**
     * 根据status查询记录
     * @param status 记录状态
     */
    default List<DataPlanDay> listByStatus(String status) {
        return selectList(this.eq(DataPlanDay.STATUS, status));
    }

}