package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redteamobile.nobel.model.entity.PayOrder;
import com.redteamobile.nobel.model.enums.EnumPayOrderStatus;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PayOrderDAO extends BaseMapper<PayOrder> {

    default PayOrder getByPayOderNo(String payOrderNo) {
        return this.selectOne(new QueryWrapper<>(PayOrder.builder().payOrderNo(payOrderNo).build()));
    }

    default Boolean updatePayOrderStatus(String payOrderNo, EnumPayOrderStatus payOrderStatus) {
        int update = this.update(PayOrder.builder().payStatus(payOrderStatus.name()).build(), new UpdateWrapper<>(PayOrder.builder().payOrderNo(payOrderNo).build()));
        return update > 0;
    }

    default int flushPayOrderConfirmInfo(String payOrderNo, PayOrder payOrder) {
        return this.update(PayOrder.builder().outPayOrderNo(payOrder.getOutPayOrderNo())
                .paymentMethodsId(payOrder.getPaymentMethodsId()).build(), new UpdateWrapper<>(PayOrder.builder().payOrderNo(payOrderNo).build()));
    }

    default PayOrder getByBizOderNo(String orderNo){
        return this.selectOne(new QueryWrapper<>(PayOrder.builder().bizOrderNo(orderNo).build()));
    }

    default Boolean updatePayOrder(String payOrderNo, Integer paymentMethodsId){
        UpdateWrapper<PayOrder> updateWrapper =
                new UpdateWrapper<>(PayOrder.builder().payOrderNo(payOrderNo).build());
        return this.update(PayOrder.builder()
                .paymentMethodsId(paymentMethodsId)
                .build(), updateWrapper)>0;
    }

    default Boolean updatePayOrder(String payOrderNo, Integer paymentMethodsId, String outPaymentOrderNo){
        UpdateWrapper<PayOrder> updateWrapper =
                new UpdateWrapper<>(PayOrder.builder().payOrderNo(payOrderNo).build());
        return this.update(PayOrder.builder()
                .paymentMethodsId(paymentMethodsId)
                .outPayOrderNo(outPaymentOrderNo).build(), updateWrapper)>0;
    };

  default List<PayOrder> queryWaitingRefundOrder() {
    return this.selectList(new QueryWrapper<>(PayOrder.builder().payStatus(EnumPayOrderStatus.REFUNDING.name()).build()));
  }
}
