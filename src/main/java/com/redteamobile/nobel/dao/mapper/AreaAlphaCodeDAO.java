package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redteamobile.nobel.model.entity.AreaAlphaCode;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public interface AreaAlphaCodeDAO extends BaseMapper<AreaAlphaCode> {

    default List<AreaAlphaCode> selectAreaIncludeCode(String code) {
        List<AreaAlphaCode> allAreaAlphaCodes = getAllAreaAlpha();
        return allAreaAlphaCodes.stream()
                .filter(areaAlphaCode -> areaAlphaCode.getCode().contains(code))
                .collect(Collectors.toList());
    }

    default List<AreaAlphaCode> getAllAreaAlpha() {
        QueryWrapper<AreaAlphaCode> queryWrapper = new QueryWrapper<>();
        return this.selectList(queryWrapper);
    }

    default Optional<AreaAlphaCode> selectAreaEqualCode(String code) {
        List<AreaAlphaCode> allAreaAlphaCodes = getAllAreaAlpha();
        return allAreaAlphaCodes.stream()
                .filter(areaAlphaCode -> areaAlphaCode.getCode().equals(code))
                .findFirst();
    }

    default Optional<AreaAlphaCode> selectAreaEqualId(Integer areaId){
        List<AreaAlphaCode> allAreaAlphaCodes = getAllAreaAlpha();
        return allAreaAlphaCodes.stream()
                       .filter(areaAlphaCode -> areaAlphaCode.getAreaId().equals(areaId))
                       .findFirst();
    }
}
