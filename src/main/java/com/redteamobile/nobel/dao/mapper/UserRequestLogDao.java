package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redteamobile.nobel.model.entity.UserRequestLog;
import com.redteamobile.nobel.model.vo.UserRequestLogAbnormalIpVO;
import com.redteamobile.nobel.util.DateUtils;
import org.apache.ibatis.annotations.Param;
import org.joda.time.DateTime;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UserRequestLogDao extends MybatisBaseMapper<UserRequestLog> {

    default int insertOne(UserRequestLog userRequestLog) {
        return this.insert(userRequestLog);
    }

    default List<UserRequestLog> selectListByDate(Date analyseDate) {
        Date today = DateUtils.getDay(analyseDate);
        Date tomorrow = DateUtils.addDays(today, 1);
//        UserRequestLog build = UserRequestLog.builder().createTime(today).build();
        QueryWrapper<UserRequestLog> queryWrapper = new QueryWrapper<>();
        String todayStr = DateUtils.format(today, DateUtils.DEFAULT_DATE_FORMAT);
        String tomorrowStr = DateUtils.format(tomorrow, DateUtils.DEFAULT_DATE_FORMAT);
        queryWrapper.between("create_time", todayStr,tomorrowStr);
        queryWrapper.orderByDesc("create_time");
        return this.selectList(queryWrapper);
    }

    /**
     * 查询从startTime到现在，同一个IP登陆的user_id数量大于countThreshold的IP以及相应的userId数量
     * @param startTime 开始统计的时间
     * @param countThreshold 查询userId数量的阈值
     * @return 对象列表
     */
    List<UserRequestLogAbnormalIpVO>  listIpAndUserIdCountByCreateTimeAndCountThreshold(@Param("startTime") DateTime startTime, @Param("countThreshold") int countThreshold);

}
