package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.constant.PackageTagScopeEnum;
import com.redteamobile.nobel.model.entity.mybatis.PackageTag;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 套餐标签信息 数据库操作接口类
 *
 * <AUTHOR>
 * @since 2023-06-16 10:37:39
 **/
@Mapper
public interface PackageTagMapper extends MybatisBaseMapper<PackageTag> {

    default List<PackageTag> selectByIds(List<Long> packageTagIds) {
        return selectList(in(PackageTag.ID, packageTagIds).eq(PackageTag.SCOPE, PackageTagScopeEnum.COMMON.name()));
    }

    default List<PackageTag> selectByIdsAndScope(List<Long> packageTagIds, String scope) {
        return selectList(in(PackageTag.ID, packageTagIds).eq(PackageTag.SCOPE, scope));
    }

    default List<PackageTag> selectAllByNameCode(String nameCode) {
        return selectList(eq(PackageTag.NAME_CODE, nameCode));
    }
}
