package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.model.entity.mybatis.DataPlanNetwork;
import com.redteamobile.nobel.model.entity.mybatis.Network;
import com.redteamobile.nobel.model.vo.NetworkVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * Network数据库操作接口类
 **/
@Mapper
public interface NetworkMapper extends MybatisBaseMapper<Network>{

  /**
   * 根据deleted查询记录
   * @param deleted 逻辑删除字段, 0: 未删除；1: 已删除
   */
  List<NetworkVO> listByDeleted(@Param("deleted") Integer deleted);

  /**
   * 插入新纪录
   */
  default int insertRecord(Network entity) {
    return insert(entity);
  };

}