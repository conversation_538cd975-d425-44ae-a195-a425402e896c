package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.model.entity.RequestLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description request_log表对应的dao
 * <AUTHOR>
 * @Date 2021/7/5 3:15 下午
 */
@Mapper
public interface RequestLogDAO extends MybatisBaseMapper<RequestLog> {

    /**
     * 插入记录
     * @param entity 新记录
     * @return 插入记录数
     */
    default int insertRecord(RequestLog entity) {
        return insert(entity);
    }

}
