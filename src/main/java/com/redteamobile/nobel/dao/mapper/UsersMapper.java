package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redteamobile.nobel.model.entity.mybatis.Users;
import com.redteamobile.nobel.util.ObjectUtils;
import com.redteamobile.nobel.util.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.joda.time.DateTime;

import java.util.List;


/**
 * Users数据库操作接口类
 **/
@Mapper
public interface UsersMapper extends MybatisBaseMapper<Users> {

    /**
     * 根据自增id查询记录
     */
    default Users queryById(Integer id) {
        if (ObjectUtils.isNull(id)) {
            return null;
        }
        return selectById(id);
    }

    /**
     * 根据email查询记录，多个时取第一个
     */
    default Users queryByEmail(String email) {
        if (StringUtils.isNullOrBlank(email)) {
            return null;
        }
        return selectFirst(this.eq(Users.EMAIL, email));
    }

    /**
     * 根据email和status查询记录，多个时取第一个
     */
    default Users queryByEmailAndStatus(String email, String status) {
        if (StringUtils.hasBlank(email, status)) {
            return null;
        }
        return selectFirst(this.eq(Users.EMAIL, email).eq(Users.STATUS, status));
    }

    /**
     * 根据email和status查询记录，多个时取第一个
     */
    default Integer queryCountByEmailAndStatus(String email, String status) {
        if (StringUtils.hasBlank(email, status)) {
            return 0;
        }
        return selectCount(this.eq(Users.EMAIL, email).eq(Users.STATUS, status));
    }

    /**
     * 根据linkCode查询记录
     * @param linkCode UUID 激活账号、找回密码时，在邮件的URL中使用
     * @return 记录
     */
    default Users queryByLinkCode(String linkCode) {
        if (StringUtils.isNullOrBlank(linkCode)) {
            return null;
        }
        return selectFirst(this.eq(Users.LINK_CODE, linkCode));
    }

    /**
     * 根据email和status查询记录
     * @param appleUserId 苹果系统中的苹果用户ID
     * @param status 激活状态
     * @return 记录
     */
    default Users queryByAppleUserIdAndStatus(String appleUserId, String status) {
        if (StringUtils.hasBlank(appleUserId, status)) {
            return null;
        }
        QueryWrapper<Users> condition = this.eq(Users.APPLE_USER_ID, appleUserId)
            .eq(Users.STATUS, status);

        return selectFirst(condition);
    }

    /**
     * 根据id更新email
     * @param id 表自增id
     * @param email 邮箱
     * @return 更新记录数
     */
    default int updateEmailById(Integer id, String email) {
        if (ObjectUtils.isNull(id) || StringUtils.isNullOrBlank(email)) {
            return 0;
        }
        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder().email(email).build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新status和activeTime
     * @param id 表自增id
     * @param status 激活状态，ACTIVE:记录已激活；INACTIVE:记录未激活
     * @param activeTime 记录激活时间
     * @return 更新记录数
     */
    default int updateStatusAndActiveTimeById(Integer id, String status, DateTime activeTime) {
        if (ObjectUtils.isNull(id) || StringUtils.isNullOrBlank(status)) {
            return 0;
        }

        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder().status(status).activeTime(activeTime).build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新记录的注册时间
     * @param id 表自增id
     * @param registerTime 用户的注册时间
     * @return 更新记录数
     */
    default int updateRegisterTimeById(Integer id, DateTime registerTime) {
        if (ObjectUtils.isNull(id)) {
            return 0;
        }
        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder().registerTime(registerTime).build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新appleUserId
     * @param id 表自增ID
     * @param loginTime 登陆的时间
     * @return 更新记录数
     */
    default int updateLoginTimeById(Integer id, DateTime loginTime) {
        if (ObjectUtils.isNull(id)) {
            return 0;
        }
        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder()
            .loginTime(loginTime)
            .build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新password
     * @param id 自增id
     * @param password 密码
     * @return 更新记录数
     */
    default int updatePasswordById(Integer id, String password) {
        if (ObjectUtils.isNull(id) || StringUtils.isNullOrBlank(password)) {
            return 0;
        }
        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder()
            .password(password)
            .build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新appleUserId
     * @param id 表自增ID
     * @param appleUserId 苹果系统中的苹果用户ID
     * @return 更新记录数
     */
    default int updateAppleUserIdById(Integer id, String appleUserId) {
        if (ObjectUtils.isNull(id) || StringUtils.isNullOrBlank(appleUserId)) {
            return 0;
        }
        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder()
            .appleUserId(appleUserId)
            .build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新linkCode和linkCodeExpiredTime
     * @param id 表自增ID
     * @param linkCode UUID 激活账号、找回密码时，在邮件的URL中使用
     * @param linkCodeExpiredTime linkCode的过期时间
     * @return 更新记录数
     */
    default int updateLinkCodeById(Integer id, String linkCode, DateTime linkCodeExpiredTime) {
        if (ObjectUtils.isNull(id)) {
            return 0;
        }
        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        Users updateEntity = Users.builder()
            .linkCode(linkCode)
            .linkCodeExpiredTime(linkCodeExpiredTime)
            .build();

        return update(updateEntity, condition);
    }

    /**
     * 根据id更新times, 字段times的更新是在记录当前值的基础上加上times
     * @param id 表自增ID
     * @param times 增加登陆的次数
     * @return 更新记录数
     */
    int updateLoginTimes(@Param("id") Integer id, @Param("times")Integer times);

    /**
     * 根据id更新times和loginTime, 字段times的更新是在记录当前值的基础上加上times
     * @param id 表自增ID
     * @param times 增加登陆的次数
     * @param loginTime 登陆时间
     * @return 更新记录数
     */
    int updateLoginTimesAndLoginTimeById(@Param("id") Integer id, @Param("times")Integer times, @Param("loginTime") DateTime loginTime);

    /**
     * 根据id将updateEntity中的非null属性更新到数据库中
     * @param id 表自增id
     * @param updateEntity 更新属性的集合
     * @return 更新记录数
     */
    default int updateUserById(Integer id, Users updateEntity) {
        if (ObjectUtils.isNull(id)) {
            return 0;
        }

        QueryWrapper<Users> condition = this.eq(Users.ID, id);

        return update(updateEntity, condition);
    }

    /**
     * 插入新记录
     * @return 插入记录数
     */
    default int insertRecord(Users entity) {
        return insert(entity);
    }

    /**
     * 根据email删除记录，当email存在多条记录的时候会都删除
     * @param email 账号邮箱
     * @return 删除记录数
     */
    default int deleteRecordByEmail(String email) {
        if (StringUtils.isNullOrBlank(email)) {
            return 0;
        }
        return delete(this.eq(Users.EMAIL, email));
    }

    default int updateToyotaMerchantId(Integer id, Long toyotaMerchantId) {
        return update(null, set(Users.TOYOTA_MERCHANT_ID, toyotaMerchantId).eq(Users.ID, id));
    }

    default List<Users> queryAllByIdList(List<Integer> ids) {
        return selectList(this.in(Users.ID, ids));
    }
}
