package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redteamobile.nobel.model.entity.mybatis.Customer;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * Customer数据库操作接口类
 **/
@Mapper
public interface CustomerMapper extends MybatisBaseMapper<Customer> {

    int insert(Customer entity);

    /**
     * 查询所有记录
     */
    default List<Customer> listAll() {
        return queryAll();
    }

    default Customer queryByIdAndDeleted(Long id, Integer deleted) {
        QueryWrapper<Customer> condition = this.eq(Customer.ID, id).eq(Customer.DELETED, deleted);
        return selectFirst(condition);
    }

    default Customer queryByEmailAndDeleted(String email, Integer deleted) {
        QueryWrapper<Customer> condition = this.eq(Customer.EMAIL, email).eq(Customer.DELETED, deleted);
        return selectFirst(condition);
    }
}