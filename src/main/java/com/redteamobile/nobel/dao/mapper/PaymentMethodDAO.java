package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redteamobile.nobel.model.entity.PaymentMethods;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PaymentMethodDAO extends BaseMapper<PaymentMethods> {

    default PaymentMethods getValidPayMethod(Integer methodId) {
        return this.selectById(methodId);
    }
}
