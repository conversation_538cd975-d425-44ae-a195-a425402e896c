package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redteamobile.nobel.model.entity.ShareUser;
import org.springframework.stereotype.Repository;

@Repository
public interface ShareUserDAO extends BaseMapper<ShareUser> {

    default int countByShareUser(Integer userId) {
        return this.selectCount(new QueryWrapper<>(ShareUser.build().setShareUserId(userId)));
    }
}
