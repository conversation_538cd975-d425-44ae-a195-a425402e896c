package com.redteamobile.nobel.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redteamobile.nobel.constant.StatusTypeEnum;
import com.redteamobile.nobel.model.entity.PrivateEmail;
import com.redteamobile.nobel.model.enums.EnumLifeCycle;
import com.redteamobile.nobel.util.DateUtils;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 隐私邮箱DAO
 *
 * <AUTHOR>
 */
@Repository
public interface PrivateEmailDAO extends BaseMapper<PrivateEmail> {

    /**
     * 分配一个没有被使用的隐私邮箱
     *
     * @return {@link PrivateEmail}
     */
    @Select("SELECT * FROM private_email "
                    + "WHERE used = 0 "
                    + "AND status = 'ACTIVE' "
                    + "ORDER BY id "
                    + "LIMIT 1")
    PrivateEmail allocation();

    default int updateUsed(Integer id, Integer used) {
        return this.updateById(PrivateEmail.builder()
                                       .id(id)
                                       .used(used)
                                       .updateTime(DateUtils.getCurrentDateTime().toDate())
                                       .build());
    }

    @Update("update private_email set used = #{used} where id =#{id}")
    int preUsed(Integer id, Integer used);

    default int countLeftEmail(Integer usedStatus){
        QueryWrapper<PrivateEmail> query = new QueryWrapper<>(PrivateEmail.builder()
                                    .used(usedStatus)
                                                                      .status(StatusTypeEnum.ACTIVE.name())
                                                                      .build());
        return this.selectCount(query);
    }

}
