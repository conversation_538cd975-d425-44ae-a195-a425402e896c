package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.model.entity.mybatis.UserDevice;
import com.redteamobile.nobel.model.vo.UserDeviceAbnormalUuidVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.joda.time.DateTime;

import java.util.List;


/**
 * UserDevice数据库操作接口类
 **/
@Mapper
public interface UserDeviceMapper extends MybatisBaseMapper<UserDevice>{

  /**
   * 插入新纪录
   * @param entity 新纪录
   * @return 插入记录数
   */
  default int insertRecord(UserDevice entity) {
    return insert(entity);
  }

  /**
   * 查询从startTime到现在，同一个uuid登陆的user_id数量大于countThreshold的uuid以及相应的userId数量
   * @param startTime 开始统计的时间
   * @param countThreshold 查询userId数量的阈值
   * @return 对象列表
   */
  List<UserDeviceAbnormalUuidVO> listUuidAndUserIdCountByCreateTimeAndCountThreshold(@Param("startTime") DateTime startTime, @Param("countThreshold") int countThreshold);

}