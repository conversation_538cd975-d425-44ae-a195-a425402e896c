package com.redteamobile.nobel.aop;

import com.redteamobile.nobel.log.SocketLogger;
import com.redteamobile.nobel.service.po.RequestLogService;
import com.redteamobile.nobel.util.HttpRequestUtils;
import com.redteamobile.nobel.util.JsonUtils;
import com.redteamobile.nobel.util.MDCUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

@Aspect
@Component
public class LoggingAspect {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RequestLogService requestLogService;

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *) " +
            "|| within(@org.springframework.stereotype.Controller *)")
    public void springBeanPointcut() {
    }

    @Pointcut("within(com.redteamobile.nobel.controller..*)")
    public void applicationPackagePointcut() {
    }

    @Around("applicationPackagePointcut() && springBeanPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        Map<String, String> attribute = (Map<String,String>)request.getAttribute(
            HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);

        String controllerName = joinPoint.getSignature().getDeclaringType().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        HttpRequestUtils.setControllerName(controllerName);
        HttpRequestUtils.setControllerMethodName(methodName);
        MDCUtils.setRequestControllerName(controllerName);
        MDCUtils.setRequestMethodName(methodName);
        MDCUtils.setPathVariable(attribute);

        Object result = joinPoint.proceed();
        String responseBody = JsonUtils.toJson(result);
        if (responseBody != null && responseBody.length() > 500) {
            responseBody = responseBody.substring(0, 500);
        }
        Long requestTimeSpan = HttpRequestUtils.getRequestTimeSpan();

        MDCUtils.setResponseBody(responseBody);
        MDCUtils.setRequestTs(String.valueOf(requestTimeSpan));
        SocketLogger.getLogger().info(" ");
        requestLogService.log();
        return result;
    }
}
