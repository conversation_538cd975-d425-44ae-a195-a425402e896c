package com.redteamobile.risk.blacklist.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EnumRequestRiskBlackListType {
    /**
     * 未知黑名单(不推荐)
     *
     * @deprecated
     */
    @Deprecated
    UNKNOWN(0, "UNKNOWN"),
    /**
     * 针对用户的黑名单
     */
    USER(1, "USER"),
    /**
     * 针对设备的黑名单
     */
    DEVICE(2, "DEVICE"),
    /**
     * 针对IP的黑名单
     */
    IP(3, "IP"),
    /**
     * 针对EMAIL的黑名单
     */
    EMAIL(4, "EMAIL"),
    /**
     * 针对EID的黑名单
     */
    EID(5, "EID"),
    ;
    private Integer code;
    private String name;

}
