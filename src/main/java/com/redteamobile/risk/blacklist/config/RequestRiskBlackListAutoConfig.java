package com.redteamobile.risk.blacklist.config;

import com.redteamobile.nobel.config.BaseWebMvcConfigurationSupport;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RequestRiskBlackListAutoConfig extends BaseWebMvcConfigurationSupport {

    @Bean
    @ConditionalOnMissingBean(RequestRiskBlackListConfig.class)
    public RequestRiskBlackListConfig requestRiskBlackListConfig() {
        return RequestRiskBlackListConfig.builder()
                .base(RequestRiskBlackListBase.builder().build())
                .build();
    }


}

