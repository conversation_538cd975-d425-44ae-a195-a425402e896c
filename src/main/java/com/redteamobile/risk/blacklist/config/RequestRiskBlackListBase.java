package com.redteamobile.risk.blacklist.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestRiskBlackListBase {

    @Builder.Default
    private Boolean enableInclude = false;
    @Builder.Default
    private Boolean enableExclude = false;
    @Builder.Default
    private List<String> includePath = new ArrayList<>();
    @Builder.Default
    private List<String> excludePath = new ArrayList<>();

}
