package com.redteamobile.risk.blacklist.strategy;

import com.redteamobile.nobel.constant.ErrorCode;
import com.redteamobile.nobel.event.UserTrack;
import com.redteamobile.nobel.handler.ServiceException;
import com.redteamobile.nobel.service.redteago.BlackListService;
import com.redteamobile.risk.blacklist.config.RequestRiskBlackListConfig;
import com.redteamobile.risk.blacklist.enums.EnumRequestRiskBlackListType;
import com.redteamobile.risk.blacklist.model.BlackListBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

@Order(2)
@Component
public class UserBlackListHandle extends AbstractBlackList {


    @Autowired
    private BlackListService blackListService;

    @Autowired
    public UserBlackListHandle(RequestRiskBlackListConfig requestRiskBlackListConfig) {
        super(requestRiskBlackListConfig);
    }

    @Override
    protected boolean supportInner(String path, UserTrack userTrack) {
        return userTrack != null && userTrack.getUsers()
                != null && userTrack.getUsers().getId() != null;
    }


    @Override
    protected List<BlackListBO> getBlackListBO(String path, UserTrack userTrack) {
        return blackListService.getBlackList(String.valueOf(userTrack.getUsers().getId()),
                EnumRequestRiskBlackListType.USER.getCode());
    }

    @Override
    protected String blackError() { return ErrorCode.REQUEST_BLACK_LIST_ERROR_USER;}
}
