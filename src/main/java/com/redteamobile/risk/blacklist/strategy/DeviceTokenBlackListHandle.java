package com.redteamobile.risk.blacklist.strategy;

import com.redteamobile.nobel.constant.ErrorCode;
import com.redteamobile.nobel.event.UserTrack;
import com.redteamobile.nobel.handler.ServiceException;
import com.redteamobile.nobel.service.redteago.BlackListService;
import com.redteamobile.risk.blacklist.config.RequestRiskBlackListConfig;
import com.redteamobile.risk.blacklist.enums.EnumRequestRiskBlackListType;
import com.redteamobile.risk.blacklist.model.BlackListBO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;


@Order(2)
@Component
public class DeviceTokenBlackListHandle extends AbstractBlackList {

    @Autowired
    private BlackListService blackListService;


    @Autowired
    public DeviceTokenBlackListHandle(RequestRiskBlackListConfig requestRiskBlackListConfig) {
        super(requestRiskBlackListConfig);
    }

    @Override
    protected boolean supportInner(String path, UserTrack userTrack) {
        return StringUtils.isNotBlank(userTrack.getDeviceToken());
    }


    @Override
    protected List<BlackListBO> getBlackListBO(String path, UserTrack userTrack) {
        return blackListService.getBlackList(userTrack.getDeviceToken(),
                EnumRequestRiskBlackListType.DEVICE.getCode());
    }

    @Override
    protected String blackError() { return ErrorCode.REQUEST_BLACK_LIST_ERROR_DEVICE;}
}
