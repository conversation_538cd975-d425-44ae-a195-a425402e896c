package com.redteamobile.risk.ip.config;

import com.redteamobile.nobel.config.BaseWebMvcConfigurationSupport;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RiskIpAutoConfig extends BaseWebMvcConfigurationSupport {

    @Bean
    @ConditionalOnMissingBean(RiskIpConfig.class)
    public RiskIpConfig riskIpConfig() {
        return RiskIpConfig.builder()
                .base(RiskIpBase.builder().build())
                .build();
    }


}

