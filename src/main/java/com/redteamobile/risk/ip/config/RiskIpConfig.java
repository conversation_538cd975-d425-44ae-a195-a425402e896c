package com.redteamobile.risk.ip.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskIpConfig {

    @Builder.Default
    private Map<String, RiskIpPath> riskIpPathMap = new HashMap<>();
    private RiskIpBase base;
    @Builder.Default
    private List<RiskIpPath> riskIpPaths = new ArrayList<>();

    public List<String> getIpFilterPath() {
        if (base == null) {
            return new ArrayList<>();
        }
        return base.getIncludePath();
    }

    public RiskIpConfig addRiskPath(String path, RiskIpPath riskIpPath) {
        riskIpPathMap.put(path, riskIpPath);
        return this;
    }

    public RiskIpPath getRiskPath(String path) {
        return riskIpPathMap.getOrDefault(path, RiskIpPath.DEFAULT);
    }


    public void clear() {
        riskIpPathMap.clear();
    }


}
