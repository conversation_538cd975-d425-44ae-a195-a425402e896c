package com.redteamobile.akira.constant;

/**
 * 系统错误码
 */

public enum ErrorCode {
	OK("0", "REQUEST_SUCCESS", "请求成功", false),
	SYSTEM_INTERNAL_EXCEPTION("10001", "RT_SYSTEM_INTERNAL_EXCEPTION", "系统内部错误", false),
	REQUEST_INVALID_PARAMETER("10002", "REQUEST_INVALID_PARAMETER_ERROR", "请求参数不合法", true),
	REQUEST_NOT_VALID("10003", "REQUEST_NOT_VALID", "请求校验错误", true),
	REQUEST_TIME_INVALID("10004", "REQUEST_TIME_INVALID", "请求时间错误", true),
	REQUEST_JSON_FORMAT_INVALID("10005", "REQUEST_JSON_FORMAT_INVALID", "请求数据不符合JSON格式", true),
	AUTH_INVALID("10006", "AUTH_INVALID", "验签不通过", true),

	MAX_UPLOAD_SIZE_EXCEEDED("20001", "MAX_UPLOAD_SIZE_EXCEEDED_ERROR", "上传文件太大", true),


	DEVICE_NOT_FOUND("30001", "DEVICE_NOT_FOUND", "设备不存在", true),
	USER_NOT_BIND("40001", "USER_NOT_BIND", "用户未绑定", true),
	NOTIFY_STRATEGY_NOT_FOUND("20001", "NOTIFY_STRATEGY_NOT_FOUND", "通知决策不存在", true),

	;

	private String code;
	private String msg;
	private String desc;
	private Boolean ignorable = false;

	ErrorCode(String code, String msg, String desc, Boolean ignorable) {
		this.code = code;
		this.msg = msg;
		this.desc = desc;
		this.ignorable = ignorable;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Boolean getIgnorable() {
		return ignorable;
	}

	public void setIgnorable(Boolean ignorable) {
		this.ignorable = ignorable;
	}
}
