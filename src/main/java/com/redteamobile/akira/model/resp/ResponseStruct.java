package com.redteamobile.akira.model.resp;


import com.redteamobile.akira.constant.ErrorCode;

public class ResponseStruct<T> implements java.io.Serializable {

	private String reqId;
	private String code;
	private String msg;
	private T data;


	public String getReqId() {
		return reqId;
	}

	public void setReqId(String reqId) {
		this.reqId = reqId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	public boolean success() {
		return ErrorCode.OK.getCode().equalsIgnoreCase(code);
	}

}
