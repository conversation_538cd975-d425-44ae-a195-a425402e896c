package com.redteamobile.nobel.db;

import com.google.common.base.Charsets;
import com.redteamobile.nobel.util.PatternUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 自动生成DB的实体类、Repository
 */
@SuppressWarnings("hiding")
public class EntityUtilsMybatisNobel {

    private final String PRI = "PRI";
    private final String Dto = "Dto";
    private final String Dao = "Dao";
    /**
     * *********************************使用前必读*******************
     * *
     * * 使用前请将moduleName更改为自己模块的名称即可（一般情况下与数据库名一致），其他无须改动。
     * *
     * **********************************************************
     */

    private final String dbName = "Nobel"; // 数据库名称（根据自己模块做相应调整!!!务必修改^_^）
    private String tableNames = "get_balance_cache";

//    private final String base_path = "/Users/<USER>/Desktop/autocode/";
    private final String base_path = "D:\\temp\\code\\nobel\\";
    private final String bean_path = base_path + "entity";
    private final String mapper_path = base_path + "mapper";
    private final String xml_path = base_path + "xml";
    private final String po_service_path = base_path + "poService";

    private final String bean_package = "com.redteamobile.nobel.model.entity.mybatis"; //实体包路径，可以根据项目进行修改

    private final String mapper_package = "com.redteamobile.nobel.dao.mapper";//mapper包路径，可以根据项目进行修改

    private final String po_service_package = "com.redteamobile.nobel.service.po.mybatis"; //poService包路径，可以根据项目进行修改

    private final String driverName = "com.mysql.cj.jdbc.Driver";

    private final String excludeTables = "databasechangelog,databasechangeloglock";

    private final String user = "redtea"; //数据库用户名

    private final String password = "redtea.123"; //数据库密码

    private final String url = "*******************************/" + dbName + "?characterEncoding=UTF-8&useSSL=false&rewriteBatchedStatements=true&zeroDateTimeBehavior=convertToNull";

    private final String mapperEndWith = "Mapper";
    private final String poServiceEndWith = "PoService";
    private final String type_char = "char";
    //表名，多个以英文逗号分隔，为空代表对正个数据库进行生成
    private final String type_date = "date";
    private final String type_float = "float";
    private final String type_double = "double";
    private final String type_timestamp = "timestamp";
    private final String type_int = "int";
    private final String type_bigint = "bigint";
    private final String type_text = "text";
    private final String type_bit = "bit";
    private final String type_decimal = "decimal";
    private final String type_blob = "blob";
    private String tableName = null;
    private String beanName = null;
    private String mapperName = null;
    private String poServiceName = null;
    private Connection conn = null;

    public static void main(String[] args) {
        try {
            new EntityUtilsMybatisNobel().generate();
            // 自动打开生成文件的目录
//            Runtime.getRuntime().exec("cmd /c start explorer D:\\");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void init() throws ClassNotFoundException, SQLException {
        Class.forName(driverName);
        conn = DriverManager.getConnection(url, user, password);
    }

    /**
     * 获取所有的表
     *
     * @return
     * @throws SQLException
     */
    private List<String> getTables(String tableNames) throws SQLException {
        List<String> tables = new ArrayList<String>();
        if (!StringUtils.isNotEmpty(tableNames)) {
            PreparedStatement pstate = conn.prepareStatement("SHOW TABLES");
            ResultSet results = pstate.executeQuery();
            while (results.next()) {
                String tableName = results.getString(1);
                tables.add(tableName);
            }
        } else {
            String[] temps = tableNames.split(",");
            for (String string : temps) {
                tables.add(string);
            }
        }
        return tables;
    }

    private void processTable(String table) {
        StringBuffer sb = new StringBuffer(table.length());
        String tableNew = table.toLowerCase();
        String[] tables = tableNew.split("_");
        String temp = null;
        for (int i = 0; i < tables.length; i++) {
            temp = tables[i].trim();
            sb.append(temp.substring(0, 1).toUpperCase()).append(temp.substring(1));
        }
        beanName = sb.toString();
        mapperName = beanName + mapperEndWith;
        poServiceName = beanName + poServiceEndWith;
    }

    private String processTypeJava(String type) {
        if (type.indexOf(type_char) > -1) {
            return "String";
        } else if (type.indexOf(type_bigint) > -1) {
            return "Long";
        } else if (type.indexOf(type_int) > -1) {
            return "Integer";
        } else if (type.indexOf(type_date) > -1) {
            return "DateTime";
        } else if (type.indexOf(type_text) > -1) {
            return "String";
        } else if (type.indexOf(type_timestamp) > -1) {
            return "DateTime";
        } else if (type.indexOf(type_float) > -1) {
            return "Float";
        } else if (type.indexOf(type_double) > -1) {
            return "Double";
        } else if (type.indexOf(type_bit) > -1) {
            return "Boolean";
        } else if (type.indexOf(type_decimal) > -1) {
            return "java.math.BigDecimal";
        } else if (type.indexOf(type_blob) > -1) {
            return "byte[]";
        }
        throw new RuntimeException("未知的字段类型:" + type);
    }

    private String processTypeKT(String type) {
        if (type.indexOf(type_char) > -1) {
            return "String";
        } else if (type.indexOf(type_bigint) > -1) {
            return "Long";
        } else if (type.indexOf(type_int) > -1) {
            return "Integer";
        } else if (type.indexOf(type_date) > -1) {
            return "DateTime";
        } else if (type.indexOf(type_text) > -1) {
            return "String";
        } else if (type.indexOf(type_timestamp) > -1) {
            return "DateTime";
        } else if (type.indexOf(type_bit) > -1) {
            return "Boolean";
        } else if (type.indexOf(type_float) > -1) {
            return "Float";
        } else if (type.indexOf(type_double) > -1) {
            return "Double";
        } else if (type.indexOf(type_decimal) > -1) {
            return "java.math.BigDecimal";
        } else if (type.indexOf(type_blob) > -1) {
            return "byte[]";
        }
        throw new RuntimeException("未知的字段类型:" + type);
    }

    private String getDefaultValue(String type, String fieldName) {
        switch (type) {
            case "String":
                return "\"\"";
            case "Long":
                return "0L";
            case "Float":
                return "0F";
            case "Double":
                return "0.0";
            case "Integer":
            case "Int":
                return "0";
            case "DateTime":
                return "DateTime.now()";
            case "Boolean":
                return "false";
            case "java.math.BigDecimal":
                return "java.math.BigDecimal(0)";
            case "byte[]":
                return "byte[0]";
        }
        throw new RuntimeException("未知的字段类型:" + type);
    }

    private String getDefaultValueConvert(String type, String dbDefaultValue,String fieldName) {
        switch (type) {
            case "String":
                return "\"" + dbDefaultValue + "\"";
            case "Long":
                return dbDefaultValue + "L";
            case "Float":
                return dbDefaultValue + "F";
            case "Double":
            case "Int":
                return dbDefaultValue;
            case "DateTime":
                return getDateTimeStr(dbDefaultValue, fieldName);
            case "Boolean":
                return dbDefaultValue;
            case "java.math.BigDecimal":
                return "BigDecimal(" + dbDefaultValue + ")";
            case "byte[]":
                return "new byte[0]";
            default:
                throw new RuntimeException("未知的字段类型:" + dbDefaultValue);
        }
    }

    private String getDateTimeStr(String dbDefaultValue,String fieldName) {
        String s = dbDefaultValue.toLowerCase();
        if ("current_timestamp".equals(s)) {
            return "DateTime.now()";
        } else if ("0000-00-00 00:00:00".equals(s)) {
            return "DateTime.now()";
        } else {
            return "DateTime.now()";
        }
    }

    private String getDefaultValueNull(String type, String _defaultValue, String fieldName) {
        switch (type) {
            case "String":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return "\"" + _defaultValue + "\"";
                }
            case "Long":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return _defaultValue + "L";
                }
            case "Float":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return _defaultValue + "F";
                }
            case "Double":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return _defaultValue;
                }
            case "Integer":
            case "Int":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return _defaultValue;
                }
            case "DateTime":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return getDateTimeStr(_defaultValue, fieldName);
                }
            case "Boolean":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return _defaultValue;
                }
            case "java.math.BigDecimal":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return "new java.math.BigDecimal(\"" + _defaultValue + "\")";
                }
            case "byte[]":
                if (_defaultValue == null) {
                    return null;
                } else {
                    return _defaultValue;
                }
            default:
                throw new RuntimeException("未知的字段类型:" + type);
        }
    }

    private String processField(String field) {
        StringBuffer sb = new StringBuffer(field.length());
        //field = field.toLowerCase();
        String[] fields = field.split("_");
        String temp = null;
        sb.append(fields[0]);
        for (int i = 1; i < fields.length; i++) {
            temp = fields[i].trim();
            sb.append(temp.substring(0, 1).toUpperCase()).append(temp.substring(1));
        }
        return sb.toString();
    }

    /**
     * 将实体类名首字母改为小写
     *
     * @param beanName
     * @return
     */
    private String processResultMapId(String beanName) {
        try {
            return beanName.substring(0, 1).toLowerCase() + beanName.substring(1) + Dto;
        } catch (Exception e) {
            e.printStackTrace();
            return beanName;
        }
    }

    /**
     * 构建类上面的注释
     *
     * @param bw
     * @param text
     * @return
     * @throws IOException
     */
    private BufferedWriter buildClassComment(BufferedWriter bw, String text) throws IOException {
        bw.newLine();
        bw.newLine();
        bw.write("/**");
        bw.newLine();
        bw.write(" * " + text);
        bw.newLine();
        bw.write(" **/");
        return bw;
    }

    /**
     * 构建方法上面的注释
     *
     * @param bw
     * @param text
     * @return
     * @throws IOException
     */
    private BufferedWriter buildMethodComment(BufferedWriter bw, String text) throws IOException {
        bw.newLine();
        bw.write("\t/**");
        bw.newLine();
        bw.write("\t * ");
        bw.newLine();
        bw.write("\t * " + text);
        bw.newLine();
        bw.write("\t * ");
        bw.newLine();
        bw.write("\t **/");
        return bw;
    }

    /**
     * 生成实体类
     *
     * @param columns
     * @param types
     * @param comments
     * @param pkTypes
     * @throws IOException
     */
    private void buildEntityBeanJava(List<String> columns, List<String> defaultValues, List<String> types, List<String> comments, String tableComment, String tableName, List<String> pkTypes)
        throws IOException {
        File folder = new File(bean_path);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        File beanFile = new File(bean_path, beanName + ".java");
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(beanFile), Charsets.UTF_8));
        bw.write("package " + bean_package + ";");
        bw.newLine();
        bw.write("import com.baomidou.mybatisplus.annotation.FieldFill;");
        bw.newLine();
        bw.write("import com.baomidou.mybatisplus.annotation.IdType;");
        bw.newLine();
        bw.write("import com.baomidou.mybatisplus.annotation.TableField;");
        bw.newLine();
        bw.write("import com.baomidou.mybatisplus.annotation.TableId;");
        bw.newLine();
        bw.write("import com.baomidou.mybatisplus.annotation.TableLogic;");
        bw.newLine();
        bw.write("import com.baomidou.mybatisplus.annotation.TableName;");
        bw.newLine();
        bw.write("import org.joda.time.DateTime;");
        bw.newLine();

        bw = buildClassComment(bw, tableComment);
        bw.newLine();
        bw.write("@SuppressWarnings(\"serial\")");
        bw.newLine();
        bw.write("@TableName(value = \"" + tableName + "\")");
        bw.newLine();
        bw.write("@lombok.Data");
        bw.newLine();
        bw.write("@lombok.Builder");
        bw.newLine();
        bw.write("@lombok.AllArgsConstructor");
        bw.newLine();
        bw.write("@lombok.NoArgsConstructor");
        bw.newLine();
        bw.write("public class " + beanName + " extends BaseEntity {");
        bw.newLine();
        bw.newLine();
        int columnsSize = columns.size();
        for (int i = 0; i < columnsSize; i++) {
//            public final static String serviceNumber = "serviceNumber";
//            String fieldJavaType = processTypeJava(types.get(i));
            String str = processField(getComments(columns, i));
            String staticFieldName = getComments(columns, i).toUpperCase();
            bw.write("\tpublic final static String " + staticFieldName + " = \"" + getComments(columns, i) + "\"; //" + getComments(comments, i));
            bw.newLine();
        }
        bw.newLine();
        bw.newLine();

        // 打印字符类型的字段长度
        for (int i = 0; i < columnsSize; i++) {
            String type = types.get(i);
            if (!type.contains(type_char)) {
                continue;
            }
            String charLength = PatternUtils.getFirstParenthesesParam(types.get(i));
            if(StringUtils.isEmpty(charLength)) {
                continue;
            }
            String staticFieldName = columns.get(i).toUpperCase() + "_COLUMN_LENGTH";
            bw.write("\tpublic final static int " + staticFieldName + " = " + charLength + ";");
            bw.newLine();
        }
        bw.newLine();
        bw.newLine();

        for (int i = 0; i < columnsSize; i++) {
//            bw.write("\t/**" + comments.get(i) + "**/");
//            bw.newLine();
            String fieldJavaType = processTypeJava(getComments(types, i));
            String pkType = getComments(pkTypes, i);
            if (PRI.equals(pkType)) {
//                bw.write("\<EMAIL>");
//                bw.newLine();
                if("String".equals(fieldJavaType)) {
//                    bw.write("\t@GeneratedValue(generator = \"fendo_Generator\")");
//                    bw.newLine();
                    bw.write("\t@GenericGenerator(name = \"fendo_Generator\", strategy = \"uuid\")");
                    bw.newLine();
                    bw.write("\tprivate " + fieldJavaType + " " + processField(getComments(columns, i)) + " ; //" + getComments(comments, i));
                }
                else{
                    bw.write("\t@TableId(type = IdType.AUTO)");
//                    bw.write("\t@GeneratedValue(strategy = javax.persistence.GenerationType.IDENTITY)");
                    bw.newLine();
                    bw.write("\tprivate " + fieldJavaType + " " + processField(getComments(columns, i)) + "; //" + getComments(comments, i));
                }

            } else {
                String fieldDefaultValue = getDefaultValueNull(fieldJavaType, getComments(defaultValues, i), processField(getComments(columns, i)));
                String fieldName = processField(getComments(columns, i));
                if ("createTime".equalsIgnoreCase(fieldName)) {
                    bw.write("\t@TableField(value = \"create_time\", fill = FieldFill.INSERT)");
                    bw.newLine();
                }else if ("updateTime".equalsIgnoreCase(fieldName)) {
                    bw.write("\t@TableField(value = \"update_time\", fill = FieldFill.INSERT_UPDATE)");
                    bw.newLine();
                }else if ("deleted".equalsIgnoreCase(fieldName)) {
                    bw.write("\t@TableLogic");
                    bw.newLine();
                }
                bw.write("\tprivate " + fieldJavaType + " " + fieldName + " = " + fieldDefaultValue + ";  //" + getComments(comments, i));
            }
            bw.newLine();
        }
        bw.newLine();
//        // 生成get 和 set方法
//        String tempField = null;
//        String _tempField = null;
//        String tempType = null;
//        for (int i = 0; i < columnsSize; i++) {
//            tempType = processTypeJava(types.get(i));
//            _tempField = processField(columns.get(i));
//            tempField = _tempField.substring(0, 1).toUpperCase() + _tempField.substring(1);
//            bw.newLine();
//            //          bw.write("\tpublic void set" + tempField + "(" + tempType + " _" + _tempField + "){");
//            bw.write("\tpublic void set" + tempField + "(" + tempType + " " + _tempField + "){");
//            bw.newLine();
//            //          bw.write("\t\tthis." + _tempField + "=_" + _tempField + ";");
//            bw.write("\t\tthis." + _tempField + " = " + _tempField + ";");
//            bw.newLine();
//            bw.write("\t}");
//            bw.newLine();
//            bw.newLine();
//            bw.write("\tpublic " + tempType + " get" + tempField + "(){");
//            bw.newLine();
//            bw.write("\t\treturn this." + _tempField + ";");
//            bw.newLine();
//            bw.write("\t}");
//            bw.newLine();
//        }
        bw.newLine();
        bw.write("}");
        bw.newLine();
        bw.flush();
        bw.close();
    }

    private String getComments(List<String> comments, int i) {
        String s = comments.get(i);
        if (s != null) {
            s = s.replace("\r", " ")
                .replace("\n", " ");
        }
        return s;
    }

    /**
     * 生成实体类
     *
     * @param columns
     * @param types
     * @param comments
     * @throws IOException
     */
    private void buildEntityBeanKT(List<String> columns, List<String> nullable, List<String> defaultValue, List<String> types, List<String> comments, String tableComment, String tableName)
        throws IOException {
        File folder = new File(bean_path);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        File beanFile = new File(bean_path, beanName + ".kt");
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(beanFile), Charsets.UTF_8));
        bw.write("package " + bean_package);
        bw.newLine();
        bw.write("import java.io.Serializable");
        bw.newLine();
//        bw.write("import javax.persistence.GeneratedValue");
//        bw.newLine();
        bw = buildClassComment(bw, tableComment);
        bw.newLine();
        bw.write("@javax.persistence.Table(name = \"" + tableName + "\")");
        bw.newLine();
        bw.write("class " + beanName + " : Serializable {");
        bw.newLine();
        bw.write("\tcompanion object {");
        bw.newLine();
        int size = columns.size();
        for (int i = 0; i < size; i++) {
            String str = processField(getComments(columns, i));
            bw.write("\t\tval _" + str + " =  \"" + str + "\" //" + getComments(comments, i));
            bw.newLine();
        }
        bw.write("\t}");
        bw.newLine();
        bw.newLine();
        for (int i = 0; i < size; i++) {
//            bw.write("\t/**" + comments.get(i) + "**/");
//            bw.newLine();
            String fieldJavaType = processTypeKT(getComments(types, i));
            String defaultV = getComments(defaultValue, i);
            String fieldName = processField(getComments(columns, i));
            if (!"NO".equals(getComments(nullable, i))) {
                //如果可为空
                defaultV = getDefaultValueNull(fieldJavaType, defaultV, fieldName);
                fieldJavaType += "?";
            } else {
                if (defaultV == null) {
                    defaultV = getDefaultValue(fieldJavaType, fieldName);
                } else {
                    defaultV = getDefaultValueConvert(fieldJavaType, defaultV,fieldName);
                }
            }
            if (i == 0 && "String".equals(fieldJavaType)) {
//                bw.write("\<EMAIL>");
//                bw.newLine();
//                bw.write("\t@GeneratedValue(generator = \"UUID\")");
//                bw.newLine();
                bw.write("\tvar " + fieldName + " = getNewUUID();");
            } else {
                bw.write("\tvar " + fieldName + ":" + fieldJavaType + " = " + defaultV);
            }
            bw.write("  //" + getComments(comments, i));
//            bw.newLine();
            bw.newLine();
        }
        bw.write("}");
        bw.newLine();
        bw.flush();
        bw.close();
    }

    /**
     * 构建Mapper文件
     *
     * @throws IOException
     */
    private void buildMapperJava(List<String> columns, String pkType) throws IOException {
        File folder = new File(mapper_path);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        String id = processField(getComments(columns, 0));
        String record = processResultMapId(beanName);
        File mapperFile = new File(mapper_path, mapperName + ".java");
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(mapperFile), "utf-8"));
        bw.write("package " + mapper_package + ";");
        bw.newLine();
        bw.newLine();
        bw.write("import " + bean_package + "." + beanName + ";");
        bw.newLine();
        bw.newLine();
        bw.write("import org.apache.ibatis.annotations.Mapper;");
        bw.newLine();
        bw = buildClassComment(bw, beanName + "数据库操作接口类");
        bw.newLine();
        bw.write("@Mapper");
        bw.newLine();
        bw.write("public interface " + mapperName + " extends MybatisBaseMapper<" +beanName + ">{");
        bw.newLine();
        bw.newLine();
//        bw.write("  int insert("+mapperName.substring(1)+" entity);");
        bw.write("    /**\n" +
            "     * 插入新纪录\n" +
            "     */");
        bw.newLine();
        bw.write("    default int insertRecord(" + beanName + " entity) { return insert(entity); };");
        bw.newLine();
        bw.newLine();
        bw.write("}");
        bw.flush();
        bw.close();
    }

    public void buildPoService() throws IOException{
        File folder = new File(po_service_path);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        File poServiceFile = new File(po_service_path, poServiceName + ".java");
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(poServiceFile), "utf-8"));
        bw.write("package " + po_service_package + ";");
        bw.newLine();
        bw.newLine();
        bw.write("import " + mapper_package + "." + mapperName + ";");
        bw.newLine();
        bw.newLine();
        bw.write("import " + bean_package + "." + beanName + ";");
        bw.newLine();
        bw.newLine();
        bw.write("import org.springframework.stereotype.Service;");
        bw.newLine();
        bw = buildClassComment(bw, beanName + "表相关的业务方法，数据库隔离层");
        bw.newLine();
        bw.write("@Service");
        bw.newLine();
        bw.write("public class " + poServiceName + " extends BasePoService<" + mapperName + ", " + beanName + ">{");
        bw.newLine();
        bw.newLine();
        bw.write("}");
        bw.flush();
        bw.close();
    }

    /**
     * 构建Mapper文件
     *
     * @throws IOException
     */
    private void buildMapperKT(List<String> columns) throws IOException {
        File folder = new File(mapper_path);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        String id = processField(getComments(columns, 0));
        String record = processResultMapId(beanName);
        File mapperFile = new File(mapper_path, mapperName.substring(1) + "Mapper.kt");
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(mapperFile), "utf-8"));
        bw.write("package " + mapper_package);
        bw.newLine();
        bw.newLine();
        bw.write("import " + bean_package + "." + beanName);
        bw.newLine();
        bw.write("import org.apache.ibatis.annotations.Param");
        bw.newLine();
        bw.write("import com.round.web.tools.dto.BaseModelMapper");
        bw.newLine();
        bw.write("import org.springframework.stereotype.Repository");
        bw.newLine();
        bw = buildClassComment(bw, mapperName.substring(1) + "数据库操作接口类");
        bw.newLine();
        bw.newLine();
        bw.write("@Repository");
        bw.newLine();
        bw.write("interface " + mapperName.substring(1) + "Mapper" + " : BaseModelMapper<" + mapperName.substring(1) + ">");
        bw.flush();
        bw.close();
    }

    /**
     * 构建实体类映射XML文件
     *
     * @param columns
     * @param types
     * @param comments
     * @throws IOException
     */
    private void buildMapperXml(List<String> columns, List<String> types, List<String> comments) throws IOException {
        File folder = new File(xml_path);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        File mapperXmlFile = new File(xml_path, beanName + "Mapper.xml");
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(mapperXmlFile), Charsets.UTF_8));
        bw.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        bw.newLine();
        bw.write("<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" ");
        bw.newLine();
        bw.write("    \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">");
        bw.newLine();
        bw.write("<mapper namespace=\"" + mapper_package + "." + mapperName + "\">");
        bw.newLine();
        bw.newLine();

        /*bw.write("\t<!--实体映射-->");
        bw.newLine();
        bw.write("\t<resultMap id=\"" + this.processResultMapId(beanName) + "ResultMap\" type=\"" + beanName + "\">");
        bw.newLine();
        bw.write("\t\t<!--" + comments.get(0) + "-->");
        bw.newLine();
        bw.write("\t\t<id property=\"" + this.processField(columns.get(0)) + "\" column=\"" + columns.get(0) + "\" />");
        bw.newLine();
        int size = columns.size();
        for ( int i = 1 ; i < size ; i++ ) {
            bw.write("\t\t<!--" + comments.get(i) + "-->");
            bw.newLine();
            bw.write("\t\t<result property=\""
                    + this.processField(columns.get(i)) + "\" column=\"" + columns.get(i) + "\" />");
            bw.newLine();
        }
        bw.write("\t</resultMap>");

        bw.newLine();
        bw.newLine();
        bw.newLine();*/

        // 下面开始写SqlMapper中的方法
        // this.outputSqlMapperMethod(bw, columns, types);
//        buildSQL(bw, columns, types);

        bw.write("</mapper>");
        bw.flush();
        bw.close();
    }

    private void buildSQL(BufferedWriter bw, List<String> columns, List<String> types) throws IOException {
        int size = columns.size();
        // 通用结果列
        bw.write("\t<!-- 通用查询结果列-->");
        bw.newLine();
        bw.write("\t<sql id=\"Base_Column_List\">");
        bw.newLine();

//        bw.write("\t\t id,");
        for (int i = 0; i < size; i++) {
            bw.write(" " + getComments(columns, i));
            if (i != size - 1) {
                bw.write(",");
            }
        }

        bw.newLine();
        bw.write("\t</sql>");
        bw.newLine();
        bw.newLine();

        if (true) {
            return;
        }

        // 查询（根据主键ID查询）
        bw.write("\t<!-- 查询（根据主键ID查询） -->");
        bw.newLine();
        bw.write("\t<select id=\"get" + beanName + "ById\" resultType=\""
            + processResultMapId(beanName) + "\" parameterType=\"java.lang." + processTypeJava(getComments(types, 0)) + "\">");
        bw.newLine();
        bw.write("\t\t SELECT");
        bw.newLine();
        bw.write("\t\t <include refid=\"Base_Column_List\" />");
        bw.newLine();
        bw.write("\t\t FROM " + tableName);
        bw.newLine();
        bw.write("\t\t WHERE " + getComments(columns, 0) + " = #{" + processField(getComments(columns, 0)) + "}");
        bw.newLine();
        bw.write("\t</select>");
        bw.newLine();
        bw.newLine();
        // 查询完


        // 删除（根据主键ID删除）
        bw.write("\t<!--删除：根据主键ID删除-->");
        bw.newLine();
        bw.write("\t<delete id=\"delete" + beanName + "ById\" parameterType=\"java.lang." + processTypeJava(getComments(types, 0)) + "\">");
        bw.newLine();
        bw.write("\t\t DELETE FROM " + tableName);
        bw.newLine();
        bw.write("\t\t WHERE " + getComments(columns, 0) + " = #{" + processField(getComments(columns, 0)) + "}");
        bw.newLine();
        bw.write("\t</delete>");
        bw.newLine();
        bw.newLine();
        // 删除完


        // 添加insert方法
//        bw.write("\t<!-- 添加 -->");
//        bw.newLine();
//        bw.write("\t<insert id=\"insert\" parameterType=\"" + processResultMapId(beanName) + "\">");
//        bw.newLine();
//        bw.write("\t\t INSERT INTO " + tableName);
//        bw.newLine();
//        bw.write(" \t\t(");
//        for (int i = 0; i < size; i++) {
//            bw.write(columns.get(i));
//            if (i != size - 1) {
//                bw.write(",");
//            }
//        }
//        bw.write(") ");
//        bw.newLine();
//        bw.write("\t\t VALUES ");
//        bw.newLine();
//        bw.write(" \t\t(");
//        for (int i = 0; i < size; i++) {
//            bw.write("#{" + processField(columns.get(i)) + "}");
//            if (i != size - 1) {
//                bw.write(",");
//            }
//        }
//        bw.write(") ");
//        bw.newLine();
//        bw.write("\t</insert>");
//        bw.newLine();
//        bw.newLine();
        // 添加insert完


        //---------------  insert方法（匹配有值的字段）
        bw.write("\t<!-- 添加 （匹配有值的字段）-->");
        bw.newLine();
        bw.write("\t<insert id=\"add" + beanName + "Selective\" parameterType=\"" + processResultMapId(beanName) + "\">");
        bw.newLine();
        bw.write("\t\t INSERT INTO " + tableName);
        bw.newLine();
        bw.write("\t\t <trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\" >");
        bw.newLine();

        String tempField = null;
        for (int i = 0; i < size; i++) {
            tempField = processField(getComments(columns, i));
            bw.write("\t\t\t<if test=\"" + tempField + " != null\">");
            bw.newLine();
            bw.write("\t\t\t\t " + getComments(columns, i) + ",");
            bw.newLine();
            bw.write("\t\t\t</if>");
            bw.newLine();
        }

        bw.newLine();
        bw.write("\t\t </trim>");
        bw.newLine();

        bw.write("\t\t <trim prefix=\"values (\" suffix=\")\" suffixOverrides=\",\" >");
        bw.newLine();

        tempField = null;
        for (int i = 0; i < size; i++) {
            tempField = processField(getComments(columns, i));
            bw.write("\t\t\t<if test=\"" + tempField + "!=null\">");
            bw.newLine();
            bw.write("\t\t\t\t #{" + tempField + "},");
            bw.newLine();
            bw.write("\t\t\t</if>");
            bw.newLine();
        }

        bw.write("\t\t </trim>");
        bw.newLine();
        bw.write("\t</insert>");
        bw.newLine();
        bw.newLine();
        //---------------  完毕


        // 修改update方法
        bw.write("\t<!-- 修 改-->");
        bw.newLine();
        bw.write("\t<update id=\"update" + beanName + "ByIdSelective\" parameterType=\"" + processResultMapId(beanName) + "\">");
        bw.newLine();
        bw.write("\t\t UPDATE " + tableName);
        bw.newLine();
        bw.write(" \t\t <set> ");
        bw.newLine();

        tempField = null;
        for (int i = 1; i < size; i++) {
            tempField = processField(getComments(columns, i));
            bw.write("\t\t\t<if test=\"" + tempField + " != null\">");
            bw.newLine();
            bw.write("\t\t\t\t " + getComments(columns, i) + " = #{" + tempField + "},");
            bw.newLine();
            bw.write("\t\t\t</if>");
            bw.newLine();
        }

        bw.newLine();
        bw.write(" \t\t </set>");
        bw.newLine();
        bw.write("\t\t WHERE " + getComments(columns, 0) + " = #{" + processField(getComments(columns, 0)) + "}");
        bw.newLine();
        bw.write("\t</update>");
        bw.newLine();
        bw.newLine();
        // update方法完毕

        // ----- 修改（匹配有值的字段）
      /*  bw.write("\t<!-- 修 改-->");
        bw.newLine();
        bw.write("\t<update id=\"updateByPrimaryKey\" parameterType=\"" + processResultMapId(beanName) + "\">");
        bw.newLine();
        bw.write("\t\t UPDATE " + tableName);
        bw.newLine();
        bw.write("\t\t SET ");

        bw.newLine();
        tempField = null;
        for (int i = 1; i < size; i++) {
            tempField = processField(columns.get(i));
            bw.write("\t\t\t " + columns.get(i) + " = #{" + tempField + "}");
            if (i != size - 1) {
                bw.write(",");
            }
            bw.newLine();
        }

        bw.write("\t\t WHERE " + columns.get(0) + " = #{" + processField(columns.get(0)) + "}");
        bw.newLine();
        bw.write("\t</update>");
        bw.newLine();
        bw.newLine();*/
    }

    /**
     * 获取所有的数据库表注释
     *
     * @return
     * @throws SQLException
     */
    private Map<String, String> getTableComment() throws SQLException {
        Map<String, String> maps = new HashMap<String, String>();
        PreparedStatement pstate = conn.prepareStatement("SHOW TABLE STATUS");
        ResultSet results = pstate.executeQuery();
        while (results.next()) {
            String tableName = results.getString("NAME");
            String comment = results.getString("COMMENT");
            maps.put(tableName, comment);
        }
        return maps;
    }

    public void generate() throws ClassNotFoundException, SQLException, IOException {
        init();
        String prefix = "show full fields from ";
        List<String> columns = null;
        List<String> types = null;
        List<String> comments = null;
        List<String> defaultValues = null;
        List<String> nullable = null;
        List<String> pkTypes = null;
        PreparedStatement pstate = null;
        List<String> tables = getTables(tableNames);
        Map<String, String> tableComments = getTableComment();
        List<String> exincludeTableList = Arrays.stream(excludeTables.split(",")).filter(w -> !"".equals(w.trim())).collect(Collectors.toList());
        for (String table : tables) {
            if(exincludeTableList.stream().anyMatch( w -> w.equals(table))){
                continue;
            }
            columns = new ArrayList<>();
            types = new ArrayList<>();
            comments = new ArrayList<>();
            defaultValues = new ArrayList<>();
            nullable = new ArrayList<>();
            nullable = new ArrayList<>();
            pkTypes = new ArrayList<>();
            pstate = conn.prepareStatement(prefix + table);
            ResultSet results = pstate.executeQuery();
            while (results.next()) {
                pkTypes.add(results.getString("KEY"));
                columns.add(results.getString("FIELD"));
                types.add(results.getString("TYPE"));
                comments.add(results.getString("COMMENT"));
                defaultValues.add(results.getString("DEFAULT"));
                nullable.add(results.getString("NULL"));
            }
            tableName = table;
            processTable(table);
            if ("".equals(beanName)) {
                continue;
            }
            //          this.outputBaseBean();
            String tableComment = tableComments.get(tableName);
            buildEntityBeanJava(columns, defaultValues, types, comments, tableComment, tableName, pkTypes);
            String pkType = getJavaPkTypeName(types, pkTypes);
            buildMapperJava(columns, pkType);
            buildPoService();
//            buildEntityBeanKT(columns, nullable, defaultValues, types, comments, tableComment, tableName);
//            buildMapperKT(columns);
            buildMapperXml(columns, types, comments);
        }
        conn.close();
    }

    private String getJavaPkTypeName(List<String> types, List<String> pkTypes) {
        for (int i =0;i <types.size();i++) {
            String typeItem = getComments(types, i);
            String pkItem = getComments(pkTypes, i);
            if (pkItem.equals(PRI)) {
                return processTypeJava(typeItem);
            }
        }
        return "Integer";
    }
}
