package com.redteamobile.nobel.locker;


import com.redteamobile.commons.locker.Locker;
import com.redteamobile.nobel.util.SpringContextUtils;
import com.redteamobile.nobel.util.StringUtils;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;
import java.util.function.Supplier;

@EqualsAndHashCode
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisLockerTest {

    @Autowired
    private SpringContextUtils springContextUtils;
    @Autowired
    private Locker locker;

    @Test
    public void test_locker(){
        String lockKey = StringUtils.uuid();
        String lockValue = StringUtils.uuid();
        Optional<Boolean> get = locker.submit(lockKey, lockValue, () -> {
            System.out.println("get");
            return true;
        });
        Assert.assertTrue(get.isPresent());
        Assert.assertTrue(get.get());
    }


}
