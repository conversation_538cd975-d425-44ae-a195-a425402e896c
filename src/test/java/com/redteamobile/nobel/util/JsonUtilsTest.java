package com.redteamobile.nobel.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.redteamobile.nobel.model.vo.CustomerVO.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

@Slf4j
public class JsonUtilsTest {

  @Test
  public void fromJson() {
    Configuration configuration = JsonUtils.fromJson("", Configuration.class);
    log.info("configuration:[{}]", configuration);
  }
  @Test
  public void getString() {
    ObjectNode objectNode = new ObjectMapper().createObjectNode();
    String aa = JsonUtils.getString(objectNode, "aa");
    assertThat(aa, is(""));

    aa = JsonUtils.getString(objectNode.put("aa", 1), "aa");
    assertThat(aa, is("1"));

    aa = JsonUtils.getString(objectNode.put("aa", "A"), "aa");
    assertThat(aa, is("A"));
  }
}