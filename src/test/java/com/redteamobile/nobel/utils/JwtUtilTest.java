package com.redteamobile.nobel.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.redteamobile.nobel.model.entity.mybatis.Users;
import com.redteamobile.nobel.model.enums.EnumLoginType;
import com.redteamobile.nobel.service.po.mybatis.UsersPoService;
import com.redteamobile.nobel.util.Base64Util;
import com.redteamobile.nobel.util.DateUtils;
import com.redteamobile.nobel.util.JsonUtils;
import com.redteamobile.nobel.util.JwtUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import sun.security.ec.ECPrivateKeyImpl;

import java.security.InvalidKeyException;
import java.security.interfaces.ECPrivateKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Optional;

@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class JwtUtilTest {

    @Autowired
    private UsersPoService usersPoService;
    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void getToken() {
        String email = "<EMAIL>";
        Users user = new Users().setEmail("<EMAIL>").setPassword("70918d18d31da3e4ffa273f262ed1742");//usersPoService.queryActiveByEmail(email);
        String token = JwtUtil.getToken(user, 3001, EnumLoginType.ACCOUNT_LOGIN.getCode());
        log.info("token:[{}]", token);
    }

    @Test
    public void getEmailKey() {
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.vo5AyC6JvqvR2MFfqfvLXcds4GI6FRhDbPuNfwEJyxY";
        Optional<String> emailKey = JwtUtil.getEmailKey(token);
        log.info("emailKey:[{}]", emailKey.get());
    }

    @Test
    public void appleToken() throws InvalidKeyException {
        String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg4a1+GOIvl/bIsV2RYQ0c2DGk7GDBV7UnsFBbqfLwBiqgCgYIKoZIzj0DAQehRANCAASwfbxoxD1WRJAAtKblcSzIv5damRme5jW1sxK551fw6k+jxdQZiQi10/wiXGqs/7PSEg6mh5PhGxoXqeBUfxDQ";
        ECPrivateKey ecPrivateKey = new ECPrivateKeyImpl(Base64Util.decode(privateKey));
        String teamId = "7N2PTUF4G4";
        String clientId = "com.redteamobile.mobiledataplus";
        String keyId = "3T27VW9QJR";
        int validDay = 180;
        DateTime startTime = DateUtils.getDateTime();
        Date endTime = startTime.plusDays(validDay).toDate();
        String token = JWT.create().withIssuer(teamId).withIssuedAt(startTime.toDate()).withExpiresAt(endTime).withAudience("https://appleid.apple.com").withSubject(clientId).withKeyId(keyId).sign(Algorithm.ECDSA256(null, ecPrivateKey));
        log.info("token:[{}]", token);

    }

    @Test
    public void appleAuth() {
        String url = "https://appleid.apple.com/auth/token";
        String token = "*********************************************************************************************************************************************************************************************************************************************************************************************************************************";
        MultiValueMap<String, String> body= new LinkedMultiValueMap<String, String>();
        body.add("client_id", "com.redteamobile.mobiledataplus");
        body.add("client_secret", token);
        body.add("code", "test");
        body.add("grant_type", "authorization_code");
        Req req = new Req();
        req.setClient_id("com.redteamobile.mobiledataplus").setClient_secret(token).setCode("test").setGrant_type("authorization_code");
        HashMap hashMap = JsonUtils.fromJson(JsonUtils.toJson(req), HashMap.class);
        body.setAll(hashMap);
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        HttpEntity<MultiValueMap<String, String>> mapHttpEntity = new HttpEntity<>(body, headers);
        String resp = restTemplate.postForObject(url, mapHttpEntity, String.class);
        log.info("resp:[{}]", resp);
    }

    @Data
    @Accessors(chain = true)
    class Req {
        private String client_id;
        private String client_secret;
        private String code;
        private String grant_type;
    }

    private String getAppleToken() throws InvalidKeyException {
        String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg4a1+GOIvl/bIsV2RYQ0c2DGk7GDBV7UnsFBbqfLwBiqgCgYIKoZIzj0DAQehRANCAASwfbxoxD1WRJAAtKblcSzIv5damRme5jW1sxK551fw6k+jxdQZiQi10/wiXGqs/7PSEg6mh5PhGxoXqeBUfxDQ";
        ECPrivateKey ecPrivateKey = new ECPrivateKeyImpl(Base64Util.decode(privateKey));
        String teamId = "7N2PTUF4G4";
        String clientId = "com.redteamobile.mobiledataplus";
        String keyId = "3T27VW9QJR";
        int validDay = 180;
        DateTime startTime = DateUtils.getDateTime();
        Date endTime = startTime.plusDays(validDay).toDate();
        return JWT.create().withIssuer(teamId).withIssuedAt(startTime.toDate()).withExpiresAt(endTime).withAudience("https://appleid.apple.com").withSubject(clientId).withKeyId(keyId).sign(Algorithm.ECDSA256(null, ecPrivateKey));
    }
}