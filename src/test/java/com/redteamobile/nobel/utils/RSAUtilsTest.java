package com.redteamobile.nobel.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.redteamobile.commons.utils.RSAUtils;
import com.redteamobile.nobel.model.req.UpdateAESReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.BlockJUnit4ClassRunner;

import java.nio.charset.StandardCharsets;
import java.security.KeyPair;

@Slf4j
@RunWith(BlockJUnit4ClassRunner.class)
public class RSAUtilsTest {

    @Test
    public void test4() throws Exception {
//        String tr = "XRBnnNxYY1BEsx\\/nyHQM\\/vQADAJ01oBG5fAL7aF10bMM03UJsl8\\/22vTotoJjlsjIuEIXUUWNw7Mfr5n+5jtLXjXmOZZqi+FJVPs\\/v5GBBmy1lv+\\/JV09IW89pcH1Rx4AapL7l+3aIOnQaTgocFBmQ\\/iMNyTBmYu1LU8eopYcJd5BopFYQOiRPk2KV2i+D6y4qOonBgw5INMoulZ426vM\\/jjtc549kpODberfTcptBbT9Lh3JHiTEwphY6iXM0LrjueaFEj3rtkwWz2ZBP2a4hftEJa\\/DQFT25oO9fU9Y8ntubPz1u1NQ20cPGG5veeakI705uEMLT5qJVZRBIiBlGVxkWC6nJJuGRnxSux2Ob\\/OvrNndWf3MYA8hpO+Lg3RA1TvjkpR9KeMzndjLctbhbssOOM\\/BeSzzj+wM5VmMyvZj5MWTzCGl2efeZQdUVEVj86xuIRAR6LNm3AzA\\/as4vkuNJaiS+fAKr88kYCIBnAJ37QD5ThWxikPaSBo62lK";
//        byte[] bytes = Base64.decodeBase64(tr);
//        log.info("bytes is :{}", bytes);
//        log.info("bytes size is :{}", bytes.length);
//        byte[] decryptBytes = RSAUtils.privateDecrypt(bytes);
//        String s = new String(decryptBytes, StandardCharsets.UTF_8);
//        log.info("decryptString is :{}", s);
    }

    @Test
    public void testGetRSAKey() throws Exception {
//        KeyPair keyPair = RSAUtils.getKeyPair();
//        String privateKey = RSAUtils.getPrivateKey(keyPair);
//        String publicKey = RSAUtils.getPublicKey(keyPair);
//        log.info("privateKey is :{}", privateKey);
//        log.info("publicKey is :{}", publicKey);
    }

    @Test
    public void test3() throws Exception {
//        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCKzcv7Bd+jwWKD23z/LB7670bSUVBVHeb0jKjm2h1v+ygKXrR8yIcSdodmEdpgSgwbdCS6lhK+0oI18IJJS2mvlrzbq+QjSvao09JASyIwSQ9lHhP0Zbqufbjgr22dgz5xBrsKuGg01xqyztAn4dwChp5VnDgC+V3wnxTCo3GLpQIDAQAB";
//        UpdateAESReq updateAESReq = new UpdateAESReq();
//        updateAESReq.setPk(publicKey);
//        updateAESReq.setRequestId("2314234123412");
//        updateAESReq.setClientId("23413451453451");
//        ObjectMapper mapper = new ObjectMapper();
//        String str = mapper.writeValueAsString(updateAESReq);
//        byte[] sourceBytes = str.getBytes(StandardCharsets.UTF_8);
//        byte[] encryptString = RSAUtils.publicEncrypt(sourceBytes, publicKey);
//        String encryptStr = Base64.encodeBase64String(encryptString);
//
//        byte[] content = Base64.decodeBase64(encryptStr);
//        log.info("encryptStr is :{}", encryptStr);
//        byte[] decryptBytes = RSAUtils.privateDecrypt(content);
//        String s = new String(decryptBytes, StandardCharsets.UTF_8);
//        log.info("decryptString is :{}", s);
    }


    @Test
    public void testRSA() throws Exception {
//        String sourceString = "123你好";
//        byte[] sourceBytes = sourceString.getBytes(StandardCharsets.UTF_8);
//        byte[] encryptString = RSAUtils.publicEncryptOnce(sourceBytes);
//        log.info("encryptString length is :{}", encryptString.length);
//        byte[] decryptBytes = RSAUtils.privateDecryptOnce(encryptString);
//        String s = new String(decryptBytes, StandardCharsets.UTF_8);
//        log.info("decryptString is :{}", s);
    }

    @Test
    public void test() throws Exception {
//        String sourceString = "";
//        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCKzcv7Bd+jwWKD23z/LB7670bSUVBVHeb0jKjm2h1v+ygKXrR8yIcSdodmEdpgSgwbdCS6lhK+0oI18IJJS2mvlrzbq+QjSvao09JASyIwSQ9lHhP0Zbqufbjgr22dgz5xBrsKuGg01xqyztAn4dwChp5VnDgC+V3wnxTCo3GLpQIDAQAB";
//        byte[] sourceBytes = sourceString.getBytes(StandardCharsets.UTF_8);
//        byte[] encryptString = RSAUtils.publicEncrypt(sourceBytes, publicKey);
//        byte[] decryptBytes = RSAUtils.privateDecrypt(encryptString);
//        String s = new String(decryptBytes, StandardCharsets.UTF_8);
//        log.info("decryptString is :{}", s);
//
//        log.info("publicKey length is :{}", s.length());
    }

}