package com.redteamobile.nobel.utils;

import com.redteamobile.nobel.config.NobelGlobalConfig;
import com.redteamobile.nobel.model.resp.shelby.EidInfo;
import com.redteamobile.nobel.model.resp.shelby.ShelbyResponse;
import com.redteamobile.nobel.util.ShelbyUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ShelbyUtilsTest {

    @Autowired
    private ShelbyUtils shelbyUtils;
    @Autowired
    private NobelGlobalConfig nobelGlobalConfig;

    @Test
    public void validDevice() {
        String handoffToken = "ST1-G20210120-3CE0DF05-5AC1-11EB-A3FE-0242AC120006";
        Optional<EidInfo> eidInfo = shelbyUtils.validDevice(handoffToken);
        log.info("eidInfo:[{}]", eidInfo.get());
    }

    @Test
    public void registerEvent() {
        String rspServerAddress = "";
        String eventId = "FBF972EEEE584EE2B00DFA6293568EA2";
        String eid = "89049032004008882600016045247898";
        String iccid = "89852240400001278702";
        shelbyUtils.registerEvent(eid, rspServerAddress, iccid, eventId);
    }

    @Test
    public void deleteEvent() {
        String eventId = "FBF972EEEE584EE2B00DFA6293568EA2";
        String eid = "89049032004008882600016045247898";
        shelbyUtils.deleteEvent(eid, eventId);
    }

    @Test
    public void transformShelbyResponse() {
        ShelbyResponse response = new ShelbyResponse();
        response.setCode("test_code")
                .setMessage("test_message");
        EidInfo eidInfo = shelbyUtils.transformShelbyResponse(response, EidInfo.class);
        log.info("eidInfo:[{}}", eidInfo);
    }
}