package com.redteamobile.nobel.service.redteago;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AppleServiceTest {

  @Autowired
  private AppleService appleService;

  @Test
  public void revokeAppleId() {
    String accessToken = "aeeba6531f3ff4a89820996c973f8df90.0.stvy.1Bo2zS4PC3um_Z1MSveiEg";
    Integer userId = 44125;
    appleService.revokeAppleId(accessToken, userId, null);
    log.info("sdads");
  }
}