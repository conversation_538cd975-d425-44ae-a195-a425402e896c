package com.redteamobile.nobel.service.redteago.impl;

import com.redteamobile.nobel.model.entity.DataPlanOrder;
import com.redteamobile.nobel.service.redteago.NewDataPlanOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class NewDataPlanOrderServiceImplTest {

  @Autowired
  private NewDataPlanOrderService newDataPlanOrderService;

  @Test
  public void getOrderByIccid() {
    Optional<DataPlanOrder> orderByIccid = newDataPlanOrderService.getOrderByIccid("89852245280000557711");
    log.info("sdsd");
  }
}