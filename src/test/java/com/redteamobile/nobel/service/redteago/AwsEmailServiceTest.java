package com.redteamobile.nobel.service.redteago;

import com.redteamobile.nobel.config.SpringMailConfig;
import com.redteamobile.nobel.util.AmazonEmailUtil;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/5/23 3:36 PM
 */
public class AwsEmailServiceTest {

    @Test
    public void testAwsSendEmail() throws Exception {
        AwsEmailService awsEmailService = new AwsEmailService();
        SpringMailConfig springMailConfig = new SpringMailConfig();
        springMailConfig.setBcc("<EMAIL>");

        AmazonEmailUtil amazonEmailUtil = new AmazonEmailUtil();

        awsEmailService.setAmazonEmailUtil(amazonEmailUtil);
        awsEmailService.setSpringMailConfig(springMailConfig);
        awsEmailService.setFrom("<EMAIL>");
        awsEmailService.awsSendEmail("<EMAIL>", "<EMAIL>", "<EMAIL>", "hello test3", "hello test3");



    }

}
