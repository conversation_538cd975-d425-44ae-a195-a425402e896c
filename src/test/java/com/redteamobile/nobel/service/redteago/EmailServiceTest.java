package com.redteamobile.nobel.service.redteago;

import com.redteamobile.nobel.model.entity.DataPlanOrder;
import com.redteamobile.nobel.model.vo.TopupPackageOrderVO;
import com.redteamobile.nobel.service.po.DataPlanOrderService;
import com.redteamobile.nobel.service.po.mybatis.TopupPackageOrderPoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class EmailServiceTest {

    @Autowired
    private EmailService emailService;
    @Autowired
    private TopupPackageOrderPoService topupPackageOrderPoService;
    @Autowired
    private DataPlanOrderService dataPlanOrderService;

    @Test
    public void sendTopUpOrderInvoice() {
        TopupPackageOrderVO topupPackageOrderVO = topupPackageOrderPoService.queryById(9771);
        emailService.sendTopUpOrderInvoice(topupPackageOrderVO);
    }

  @Test
  public void sendAbnormalData() throws Exception {
    emailService.sendAbnormalData(null, "");
    log.info("s");
  }

  @Test
  public void sendOrderReceipt() throws Exception {
      String orderId = "22082607802617";
    DataPlanOrder dataPlanOrder = dataPlanOrderService.queryOne(orderId);
    emailService.sendOrderReceipt(dataPlanOrder);
  }
}