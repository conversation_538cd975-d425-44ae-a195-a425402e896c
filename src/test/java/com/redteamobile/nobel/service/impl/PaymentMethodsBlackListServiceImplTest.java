package com.redteamobile.nobel.service.impl;

import com.redteamobile.nobel.model.entity.mybatis.PaymentMethodsBlackList;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

public class PaymentMethodsBlackListServiceImplTest {

  public PaymentMethodsBlackList getPaymentMethodsBlackList(Integer paymentMethodId, Integer clientId, Integer areaId, Integer dcrId) {
    return new PaymentMethodsBlackList()
        .setPaymentMethodId(paymentMethodId)
        .setClientId(clientId)
        .setAreaId(areaId)
        .setDcrId(dcrId);
  }

  @Test
  public void testPaymentMethodWithNullValues() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, null, null, null));
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 1, 1, 1);
    Assertions.assertTrue(result);
  }

  @Test
  public void testPaymentMethodWithOneNullValue() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, null));
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 1, 1, 1);
    Assertions.assertTrue(result);
  }

  @Test
  public void testInputWithNullValues() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    boolean result = isPaymentMethodInBlacklist(blackList, null, 1, 1, 1);
    Assertions.assertFalse(result);
  }

  @Test
  public void testDuplicateElementsInBlacklist() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 1, 1, 1);
    Assertions.assertTrue(result);
  }

  @Test
  public void testPartialMatchInBlacklist() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 2, 3, 4));
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 2, 3, 5);
    Assertions.assertFalse(result);
  }

  @Test
  public void testPaymentMethodInBlacklist() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 1, 1, 1);
    Assertions.assertTrue(result);
  }

  @Test
  public void testPaymentMethodNotInBlacklist() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    boolean result = isPaymentMethodInBlacklist(blackList, 2, 1, 1, 1);
    Assertions.assertFalse(result);
  }

  @Test
  public void testEmptyBlacklist() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 1, 1, 1);
    Assertions.assertFalse(result);
  }

  @Test
  public void testBlacklistWithOneElement() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    boolean result = isPaymentMethodInBlacklist(blackList, 1, 1, 1, 1);
    Assertions.assertTrue(result);
  }

  @Test
  public void testBlacklistWithMultipleElements() {
    List<PaymentMethodsBlackList> blackList = new ArrayList<>();
    blackList.add(getPaymentMethodsBlackList(1, 1, 1, 1));
    blackList.add(getPaymentMethodsBlackList(2, 2, 2, 2));
    blackList.add(getPaymentMethodsBlackList(3, 3, 3, 3));
    boolean result1 = isPaymentMethodInBlacklist(blackList, 2, 2, 2, 2);
    boolean result2 = isPaymentMethodInBlacklist(blackList, 4, 4, 4, 4);
    Assertions.assertTrue(result1);
    Assertions.assertFalse(result2);
  }

  private boolean isPaymentMethodInBlacklist(List<PaymentMethodsBlackList> paymentMethodsBlackLists,
                                             Integer paymentMethodId, Integer clientId, Integer dcrId, Integer areaId) {

    return paymentMethodsBlackLists.stream()
        .anyMatch(paymentMethodsBlack -> paymentMethodsBlack.getPaymentMethodId().equals(paymentMethodId) &&
            (paymentMethodsBlack.getClientId() == null || paymentMethodsBlack.getClientId().equals(clientId)) &&
            (paymentMethodsBlack.getAreaId() == null || paymentMethodsBlack.getAreaId().equals(areaId)) &&
            (paymentMethodsBlack.getDcrId() == null || paymentMethodsBlack.getDcrId().equals(dcrId)));
  }
}
