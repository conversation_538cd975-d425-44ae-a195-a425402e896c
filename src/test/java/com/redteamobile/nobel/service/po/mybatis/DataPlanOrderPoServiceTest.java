package com.redteamobile.nobel.service.po.mybatis;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DataPlanOrderPoServiceTest {

  @Autowired
  private DataPlanOrderPoService dataPlanOrderPoService;

  @Test
  public void resetOrderStatusByTopUp() {
    String orderNo = "22111012409635";
    boolean result = dataPlanOrderPoService.resetOrderStatusByTopUp(orderNo);
    log.info("result:[{}]", result);
  }
}