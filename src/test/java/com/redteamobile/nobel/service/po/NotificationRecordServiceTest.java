package com.redteamobile.nobel.service.po;

import com.redteamobile.nobel.model.req.EsimOrderResourceReq;
import com.redteamobile.nobel.model.req.RedteaGoPaymentNotifyInfoReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class NotificationRecordServiceTest {

    @Autowired
    private NotificationRecordService notificationRecordService;

    @Test
    public void saveMammonNotify() {
        String paymentOrderId = "test_paymentOrderId";
        String paymentStatus = "test_status";
        RedteaGoPaymentNotifyInfoReq req = new RedteaGoPaymentNotifyInfoReq();
        req.setOrderId(paymentOrderId);
        req.setStatus(paymentStatus);
        notificationRecordService.saveMammonNotify(req);
    }

    @Test
    public void saveBellNotify() {
        EsimOrderResourceReq req = new EsimOrderResourceReq();
        req.setEid("test_eid");
        req.setOrderId("test_orderId");
        req.setQrResourceId(0);
        req.setResourceState("Test_resource_status");
        notificationRecordService.saveBellNotify(req);
    }

}