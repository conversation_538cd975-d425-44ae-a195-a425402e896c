package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.service.redteago.RedteaGoUserService;
import com.redteamobile.nobel.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.constraintvalidators.AbstractEmailValidator;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class UsersMapperTest {

  @Autowired
  private UsersMapper usersMapper;

  @Test
  public void listEmail() {
    Integer userId = 0;
    AbstractEmailValidator abstractEmailValidator = new AbstractEmailValidator();
    List<String> emailList = CollectionUtils.newArrayList();
    List<Object> invalidEmailList = CollectionUtils.newArrayList();
    do {
//      emailList = usersMapper.listEmail(userId);

      for (String email : emailList) {
        boolean valid = abstractEmailValidator.isValid(email, null);
        if (!valid) {
          invalidEmailList.add(email);
        }
      }
      userId += 10000;
    } while (emailList.size() == 10000);

    log.info("invalidEmailList size:[{}]", invalidEmailList.size());

  }

  @Test
  public void name() {
    String email = "wqwerqwerqwer";
    String email1 = "<EMAIL>";
    AbstractEmailValidator abstractEmailValidator = new AbstractEmailValidator();
    boolean valid = abstractEmailValidator.isValid(email, null);
    boolean valid1 = abstractEmailValidator.isValid(email1, null);
    log.info("valid email:[{}], valid1:[{}]", valid, valid1);
  }

  @Test
  public void name1() {
    String email = "wqwerqwerqwer";
    String email1 = "<EMAIL>";
    boolean valid = RedteaGoUserService.checkEmail(email);
    boolean valid1 = RedteaGoUserService.checkEmail(email1);
    log.info("valid email:[{}], valid1:[{}]", valid, valid1);
  }
}