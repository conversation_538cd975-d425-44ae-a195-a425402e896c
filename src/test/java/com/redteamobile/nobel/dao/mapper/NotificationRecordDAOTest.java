package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.model.entity.NotificationRecord;
import com.redteamobile.nobel.model.enums.BusinessIdTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class NotificationRecordDAOTest {

    @Autowired
    private NotificationRecordDAO notificationRecordDAO;

    @Test
    public void insertRecord() {
        NotificationRecord record = NotificationRecord.build()
                .setBusinessId("test_id")
                .setBusinessType(BusinessIdTypeEnum.PAYMENT_ORDER_ID.getCode())
                .setBusinessStatus("test_status")
                .setNotificationType(NotificationRecord.NotificationTypeEnum.MAMMON.getCode());
        notificationRecordDAO.insertRecord(record);
    }
}