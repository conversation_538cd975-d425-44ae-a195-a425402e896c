package com.redteamobile.nobel.dao.mapper;

import com.redteamobile.nobel.model.entity.IpadDeviceInfo;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.Assert.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class IpadDeviceInfoDAOTest {

    @Autowired
    private IpadDeviceInfoDAO ipadDeviceInfoDAO;

    @Test
    public void insertRecord() {
        String eid = "89049032004008882600016045247898";
        String imei = "";
        String productType = "";
        ipadDeviceInfoDAO.insertRecord(eid, imei, productType);
    }

    @Test
    public void queryByEid() {
        String eid = "89049032004008882600016045247898";
        IpadDeviceInfo deviceInfo = ipadDeviceInfoDAO.queryByEid(eid);
        assertThat(deviceInfo, Matchers.notNullValue());
    }

    @Test
    public void updateLoginToken() {
        String eid = "89049032004008882600016045247898";
        String lastLoginToken = "sdfas";
        Integer userId = 1232123;
        ipadDeviceInfoDAO.updateLoginTokenByEid(eid, lastLoginToken, userId);
    }

    @Test
    public void updateLoginTokenByUserId() {
        String lastLoginToken = "";
        Integer userId = 1232123;
        Integer newUserId = null;
        ipadDeviceInfoDAO.updateLoginTokenByUserId(userId, lastLoginToken, newUserId);

    }
}