package com.redteamobile.nobel.dao.mapper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class UserWhiteListDAOTest {

    @Autowired
    private UserWhiteListDAO userWhiteListDAO;

    @Test
    public void queryByUserId() {
        Integer userId = 456;
        userWhiteListDAO.queryByUserId(userId);
    }
}