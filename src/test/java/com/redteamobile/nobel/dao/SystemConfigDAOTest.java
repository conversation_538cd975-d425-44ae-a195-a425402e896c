package com.redteamobile.nobel.dao;

import com.redteamobile.nobel.config.DpoAutoLoadConfig;
import com.redteamobile.nobel.config.NobelOAuthConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SystemConfigDAOTest {

    @Autowired
    private SystemConfigDao systemConfigDao;


    @Test
    public void test_DpoAutoLoadConfig() {
        DpoAutoLoadConfig dpoAutoLoadConfig = systemConfigDao.getDpoAutoLoadConfig();
        Assert.assertNotNull(dpoAutoLoadConfig);
    }

    @Test
    public void test_getNobelOAuth() {
        NobelOAuthConfig nobelOAuth = systemConfigDao.getNobelOAuth();
        Assert.assertNotNull(nobelOAuth);
    }

}
